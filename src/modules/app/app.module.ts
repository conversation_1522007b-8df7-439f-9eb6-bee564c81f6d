import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { HttpExceptionFilter } from 'svm-nest-lib-v3';
import { AppService } from './app.service';

import { AppController } from './app.controller';

import { ZenithIntegrationModule } from 'src/micro-services/calculated-scores/calculated-scores.module';
import I18nConfigModule from '../../configs/i18n';
import { DatabaseModule, MessageBrokerModule } from '../../infras';
import { CargoTypeModule } from '../../modules-qa/cargo-type/cargo-type.module';
import { CargoModule } from '../../modules-qa/cargo/cargo.module';
import { CatalogModule } from '../../modules-qa/catalog/catalog.module';
import { ClassDispensationsModule } from '../../modules-qa/class-dispensations/class-dispensations.module';
import { ClassificationSocietyModule } from '../../modules-qa/classification-society/classification-society.module';
import { CrewGroupingModule } from '../../modules-qa/crew-grouping/crew-grouping.module';
import { DryDockingModule } from '../../modules-qa/dry-docking/dry-docking.module';
import { ElementMasterModule } from '../../modules-qa/element-master/element-master.module';
import { EventTypeModule } from '../../modules-qa/event-type/event-type.module';
import { ExternalInspectionsModule } from '../../modules-qa/external-inspections/external-inspections.module';
import { IncidentInvestigationModule } from '../../modules-qa/incident-investigation/incident-investigation.module';
import { IncidentMasterModule } from '../../modules-qa/incident-master/incident-master.module';
import { InjuryBodyModule } from '../../modules-qa/injury-body/injury-body.module';
import { InjuryMasterModule } from '../../modules-qa/injury-master/injury-master.module';
import { InjuryModule } from '../../modules-qa/injury/injury.module';
import { InternalInspectionsModule } from '../../modules-qa/internal-inspections/internal-inspections.module';
import { MaintenancePerformanceModule } from '../../modules-qa/maintenance-performance/maintenance-performance.module';
import { NykModule } from '../../modules-qa/nyk-vessels/nyk-vessel.module';
import { OtherSmsRecordsModule } from '../../modules-qa/other-sms-records/other-sms-records.module';
import { OtherTechnicalRecordsModule } from '../../modules-qa/other-technical-records/other-technical-records.module';
import { PilotTerminalFeedbackModule } from '../../modules-qa/pilot-terminal-feedback/pilot-terminal-feedback.module';
import { PlansDrawingsMasterModule } from '../../modules-qa/plans-drawings-master/plans-drawings-master.module';
import { PlansDrawingsModule } from '../../modules-qa/plans-drawings/plans-drawings.module';
import { PortStateControlModule } from '../../modules-qa/port-state-control/port-state-control.module';
import { RightShipModule } from '../../modules-qa/right-ship/right-ship.module';
import { RiskFactorModule } from '../../modules-qa/risk-factor/risk-factor.module';
import { SelfAssessmentModule } from '../../modules-qa/self-assessment/self-assessment.module';
import { ShipParticularModule } from '../../modules-qa/ship-particular/ship-particular.module';
import { StandardMasterModule } from '../../modules-qa/standard-master/standard-master.module';
import { SummaryModule } from '../../modules-qa/summary/summary.module';
import { SurveyClassInfoModule } from '../../modules-qa/survey/survey-class-info/survey-class-info.module';
import { TechnicalIssueNoteModule } from '../../modules-qa/technical-issue-note/technical-issue-note.module';
import { TerminalModule } from '../../modules-qa/terminal/terminal.module';
import { TransferTypeModule } from '../../modules-qa/transfer-type/transfer-type.module';
import { VesselOwnerBusinessModule } from '../../modules-qa/vessel-owner-business/vessel-owner-business.module';
import { VoyageSchedulerDetailsModule } from '../../modules-qa/vessel-scheduler-module/vessel-scheduler-module.module';
import { VesselScreeningModule } from '../../modules-qa/vessel-screening/vessel-screening.module';
import { VoyageStatusModule } from '../../modules-qa/voyage-status/voyage-status.module';
import { VoyageModule } from '../../modules-qa/voyage/voyage.module';
import { AnalyticalReportModule } from '../analytical-report/analytical-report.module';
import { AppTypePropertyModule } from '../app-type-property/app-type-property.module';
import { AttachmentKitModule } from '../attachment-kit/attachment-kit.module';
import { AuditChecklistModule } from '../audit-checklist/audit-checklist.module';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuditTimeTableModule } from '../audit-time-table/audit-time-table.module';
import { AuditTypeModule } from '../audit-type/audit-type.module';
import { AuditWorkspaceModule } from '../audit-workspace/audit-workspace.module';
import { AuthModule } from '../auth/auth.module';
import { AuthorityMasterModule } from '../authority-master/authority-master.module';
import { CategoryModule } from '../category/category.module';
import { CDIModule } from '../cdi/cdi.module';
import { CharterOwnerModule } from '../charter-owner/charter-owner.module';
import { CompanyFeatureVersionModule } from '../commons/company-feature-version/company-feature-version.module';
import { CompanySupportModule } from '../commons/company-support/company-support.module';
import { HealthModule } from '../commons/health/health.module';
import { MasterTableModule } from '../commons/master-table/master-table.module';
import { TimezoneModule } from '../commons/timezone/timezone.module';
import { CompanyTypeModule } from '../company-type/company-type.module';
import { CompanyModule } from '../company/company.module';
import { CarModule } from '../corrective-action-request/car.module';
import { DashboardModule } from '../dashboard/dashboard.module';
import { DepartmentModule } from '../department-master/department.module';
import { DeviceControlModule } from '../device-control/device-control.module';
import { DivisionMappingModule } from '../division-mapping/division-mapping.module';
import { DivisionModule } from '../division/division.module';
import { DMSModule } from '../dms/dms.module';
import { ExperienceModule } from '../experiences/experiences.module';
import { FeedbackModule } from '../feedback/feedback.module';
import { FleetModule } from '../fleet/fleet.module';
import { FocusRequestModule } from '../focus-request/focus-request.module';
import { GroupModule } from '../group/group.module';
import { HomepageModule } from '../homepage/homepage.module';
import { ImportConfigurationModule } from '../import-configuration/import-configuration.module';
import { InspectionMappingModule } from '../inspection-mapping/inspection-mapping.module';
import { InspectorTimeOffModule } from '../inspector-time-off/inspector-time-off.module';
import { InternalAuditReportModule } from '../internal-audit-report/internal-audit-report.module';
import { LicenseCertificationModule } from '../license-certification/license-certification.module';
import { LocationModule } from '../location/location.module';
import { MailSendModule } from '../mail-send/mail-send.module';
import { MailTemplateModule } from '../mail-template/mail-template.module';
import { MapViewModule } from '../map-view/map-view.module';
import { MobileConfigModule } from '../mobile-config/mobile-config.module';
import { NatureFindingModule } from '../nature_finding/nature-finding.module';
import { CategoryMappingModule } from '../new-category/category-mapping/category-mapping.module';
import { MainCategoryModule } from '../new-category/main-category/main-category.module';
import { SecondCategoryModule } from '../new-category/second-category/second-category.module';
import { ThirdCategoryModule } from '../new-category/third-category/third-category.module';
import { PackageModule } from '../package/package.module';
import { PlanningRequestModule } from '../planning-request/planning-request.module';
import { PmsModule } from '../pms/pms.module';
import { PortMasterModule } from '../port-master/port-master.module';
import { PrefixModule } from '../prefix/prefix.module';
import { PriorityMasterModule } from '../priority-master/priority-master.module';
import { ProvidedInspectionModule } from '../provided-inspection/provided-inspection.module';
import { ProviderConfigModule } from '../provider-config/provider-config.module';
import { PSCActionMasterModule } from '../psc-action-master/psc-action-master.module';
import { PSCDeficiencyMasterModule } from '../psc-deficiency-master/psc-deficiency-master.module';
import { RankModule } from '../rank-master/rank.module';
import { RecoverPasswordRequestModule } from '../recover-password-request/recover-password-request.module';
import { RepeatedFindingModule } from '../repeated-finding/repeated-finding.module';
import { ReportFindingModule } from '../report-finding/report-finding.module';
import { ReportHeaderModule } from '../report-template-master/report-header/report-header.module';
import { ReportTemplateModule } from '../report-template-master/report-template/report-template.module';
import { RequestTrialModule } from '../request-trial/request-trial.module';
import { ROFFileHistoryModule } from '../rof-file-history/rof-file-history.module';
import { DataGridTemplateModule } from '../save-template/data-grid-template.module';
import { SdrModule } from '../sdr/sdr.module';
import { ShipDepartmentModule } from '../ship-management/ship-department/ship-department.module';
import { ShipRankModule } from '../ship-management/ship-rank/ship-rank.module';
import { ShoreDepartmentModule } from '../shore-management/shore-department/shore-department.module';
import { ShoreRankModule } from '../shore-management/shore-rank/shore-rank.module';
import { SmsModule } from '../sms/sms.module';
import { SubscriptionPackageModule } from '../subscription-package/subscription-package.module';
import { TopicModule } from '../topic/topic.module';
import { TravelDocumentModule } from '../travel-document/travel-document.module';
import { UserAssignmentModule } from '../user-assignment/user-assignment.module';
import { UserModule } from '../user/user.module';
import { ValueChangeHistoryModule } from '../value-change-history/value-change-history.module';
import { ValueManagementModule } from '../value-management/value-management..module';
import { VesselTypeModule } from '../vessel-type/vessel-type.module';
import { VesselModule } from '../vessel/vessel.module';
import { ViqModule } from '../viq/viq.module';
import { WatchlistModule } from '../watchlist/watchlist.module';
import { WidgetModule } from '../widget/widget.module';
import { TransferDataModule } from './../transfer-data/transfer-data.module';
import { CompanyConfigurationModule } from '../company-configuration/company-configuration.module';
import { GetFileAwsModule } from '../get-file-aws/get-file-aws.module';
import { MemoryUsageCheckMiddleware } from 'src/configs/memory-usage-check.middleware';
import { PowerBIConfigModule } from '../power-bi-config/power-bi-config.module';
import { ObservedRiskModule } from '../risks/observed-risk.module';
import { CVIQVersionModule } from '../../modules-qa/cviq-version/version.module';
import { CVIQChapterModule } from '../../modules-qa/cviq-chapter/chapter.module';
import { CVIQConditionalityModule } from '../../modules-qa/cviq-conditionality/conditionality.module';
import { CVIQDetailMappingModule } from 'src/modules-qa/cviq-detail-mapping/detail-mapping.module';
import { SireViqModule } from 'src/modules-qa/sire-viq/sire-viq.module';
import { SubIncidentTypeModule } from '../../modules-qa/sub-incident-type/sub-incident-type.module';
import { VesselCompanyFeedbackModule } from 'src/modules-qa/vessel-company-feedback/vessel-company-feedback.module';
import { CategorizationMasterModule } from 'src/modules-qa/categorization-master/categorization-master.module';
import { VoyageTypeModule } from '../voyage-type/voyage-type.module';
import { RiskMatrixMasterModule } from '../risk-matrix-master/risk-matrix-master.module';

@Module({
  imports: [
    // in-memory cache [OFF]
    // CacheModule.register({
    //   ttl: 5, // cache response in 5 seconds
    //   max: 100, // maximum number of items in cache
    // }),
    // MyRedisModule, // redis cache [OFF]
    DatabaseModule, // persistent DBs
    ScheduleModule.forRoot(),
    UserModule,
    PackageModule,
    SubscriptionPackageModule,
    DeviceControlModule,
    AppTypePropertyModule,
    InternalAuditReportModule,
    RecoverPasswordRequestModule,
    RequestTrialModule,
    RankModule,
    DepartmentModule,
    PmsModule,
    SmsModule,
    SdrModule,
    ViqModule,
    MessageBrokerModule,
    I18nConfigModule,
    HealthModule,
    AuditTypeModule,
    VesselTypeModule,
    FleetModule,
    GroupModule,
    VesselModule,
    CompanyModule,
    UserModule,
    // RoleModule,
    ShoreRankModule,
    ShoreDepartmentModule,
    CharterOwnerModule,
    PortMasterModule,
    TimezoneModule,
    CDIModule,
    DMSModule,
    // CategoryModule,
    TopicModule,
    ShipDepartmentModule,
    ShipRankModule,
    AuditChecklistModule,
    CompanyFeatureVersionModule,
    LocationModule,
    CompanyFeatureVersionModule,
    MasterTableModule,
    ReportTemplateModule,
    ReportHeaderModule,
    CategoryModule,
    MainCategoryModule,
    SecondCategoryModule,
    ThirdCategoryModule,
    PlanningRequestModule,
    AuthorityMasterModule,
    PriorityMasterModule,
    ReportFindingModule,
    AuditWorkspaceModule,
    NatureFindingModule,
    InspectionMappingModule,
    MobileConfigModule,
    PSCActionMasterModule,
    PSCDeficiencyMasterModule,
    AuditTimeTableModule,
    DashboardModule,
    PrefixModule,
    FeedbackModule,
    TransferDataModule,
    CategoryMappingModule,
    DataGridTemplateModule,
    FocusRequestModule,
    CarModule,
    InspectorTimeOffModule,
    CategoryModule,
    StandardMasterModule,
    SelfAssessmentModule,
    CatalogModule,
    ElementMasterModule,
    IncidentMasterModule,
    EventTypeModule,
    TechnicalIssueNoteModule,
    ClassDispensationsModule,
    InjuryMasterModule,
    IncidentInvestigationModule,
    InjuryBodyModule,
    SurveyClassInfoModule,
    AttachmentKitModule,
    MailTemplateModule,
    MaintenancePerformanceModule,
    CompanySupportModule,
    MailSendModule,
    OtherTechnicalRecordsModule,
    DryDockingModule,
    InjuryModule,
    OtherSmsRecordsModule,
    TerminalModule,
    PlansDrawingsModule,
    InternalInspectionsModule,
    PortStateControlModule,
    ExternalInspectionsModule,
    AuditLogModule,
    ExperienceModule,
    RiskFactorModule,
    LicenseCertificationModule,
    TravelDocumentModule,
    ProvidedInspectionModule,
    TransferTypeModule,
    CargoTypeModule,
    CargoModule,
    VesselScreeningModule,
    ValueManagementModule,
    VesselOwnerBusinessModule,
    ShipParticularModule,
    SummaryModule,
    MapViewModule,
    PlansDrawingsMasterModule,
    PilotTerminalFeedbackModule,
    VesselCompanyFeedbackModule,
    ClassificationSocietyModule,
    CrewGroupingModule,
    DivisionModule,
    CompanyTypeModule,
    DivisionMappingModule,
    UserAssignmentModule,
    AnalyticalReportModule,
    RightShipModule,
    RepeatedFindingModule,
    VoyageModule,
    HomepageModule,
    WatchlistModule,
    ValueChangeHistoryModule,
    ZenithIntegrationModule,
    AuthModule,
    ImportConfigurationModule,
    WidgetModule,
    VoyageSchedulerDetailsModule,
    VoyageStatusModule,
    NykModule,
    ProviderConfigModule,
    ROFFileHistoryModule,
    CompanyConfigurationModule,
    GetFileAwsModule,
    PowerBIConfigModule,
    ObservedRiskModule,
    CVIQVersionModule,
    CVIQChapterModule,
    CVIQConditionalityModule,
    CVIQDetailMappingModule,
    SireViqModule,
    SubIncidentTypeModule,
    CategorizationMasterModule,
    VoyageTypeModule,
    RiskMatrixMasterModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    MemoryUsageCheckMiddleware,
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: CacheInterceptor,
    // }, // in-memory cache [OFF]
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MemoryUsageCheckMiddleware).forRoutes('*');
  }
}
