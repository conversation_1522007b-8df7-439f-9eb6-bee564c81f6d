# Risk Matrix Master Module

## Overview
The Risk Matrix Master module allows users to define and manage risk matrices as defined by the company. This module provides a complete CRUD interface for managing risk matrix configurations.

## Features

### 1. Create New Risk Matrix
- Users can define the number of rows and columns for the matrix
- Both fields accept only positive integers and are mandatory
- Only one risk matrix can be active at a time per company
- Save button is enabled only when both rows and columns fields are populated

### 2. List Risk Matrices
- Displays a list/grid of previously created risk matrix records
- Shows Matrix ID, Rows, Columns, Status (Active/Inactive), Created Date, Created By
- Supports filtering by status (Active/Inactive)
- Supports pagination and sorting
- Search functionality for rows/columns

### 3. View Risk Matrix Details
- View detailed information about a specific risk matrix
- Shows creator and updater information
- Displays creation and update timestamps

### 4. Update Risk Matrix
- Modify existing risk matrix configurations
- Maintains business rule: only one active matrix per company
- Tracks update history

### 5. Delete Risk Matrix
- Soft delete functionality
- Maintains data integrity

## API Endpoints

### POST `/risk-matrix-master`
Create a new risk matrix

**Request Body:**
```json
{
  "rows": 5,
  "columns": 5,
  "status": "active" // optional, defaults to "active"
}
```

### GET `/risk-matrix-master`
List risk matrices with pagination and filtering

**Query Parameters:**
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 20)
- `status`: Filter by status (active/inactive)
- `content`: Search in rows/columns
- `sort`: Sort fields (e.g., "rows:1;columns:-1")
- `createdAtFrom`: Date range filter start
- `createdAtTo`: Date range filter end

### GET `/risk-matrix-master/:id`
Get risk matrix details by ID

### PUT `/risk-matrix-master/:id`
Update an existing risk matrix

**Request Body:**
```json
{
  "rows": 6,
  "columns": 4,
  "status": "inactive"
}
```

### DELETE `/risk-matrix-master/:id`
Delete a risk matrix (soft delete)

## Validation Rules

1. **Rows**: Must be a positive integer (minimum 1)
2. **Columns**: Must be a positive integer (minimum 1)
3. **Status**: Must be either "active" or "inactive"
4. **Business Rule**: Only one risk matrix can be active per company at any time

## Database Schema

The `RiskMatrixMaster` entity includes:
- `id`: UUID primary key
- `rows`: Number of rows (integer)
- `columns`: Number of columns (integer)
- `status`: Status enum (active/inactive)
- `companyId`: Company UUID
- `createdUserId`: Creator user UUID
- `updatedUserId`: Updater user UUID
- Standard audit fields from `IdentifyEntity`

## Permissions

The module uses the following permission structure:
- Feature: `Configuration::Common::Risk Matrix Master`
- Actions: CREATE, VIEW, UPDATE, DELETE

## Error Handling

- Validates positive integers for rows and columns
- Prevents multiple active matrices per company
- Returns appropriate HTTP status codes and error messages
- Handles not found scenarios gracefully

## Usage Example

```typescript
// Create a new risk matrix
const newMatrix = await riskMatrixService.createRiskMatrix({
  rows: 5,
  columns: 5,
  status: 'active'
}, token);

// List matrices with filtering
const matrices = await riskMatrixService.listRiskMatrix({
  page: 1,
  pageSize: 10,
  status: 'active'
}, token);
``` 