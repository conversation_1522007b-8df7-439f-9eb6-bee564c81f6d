import { Entity, Column, PrimaryGeneratedColumn, Index, ManyToOne } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { StatusCommon } from '../../commons/enums';
import { DBIndexes } from '../../commons/consts/db.const';
import { Company } from '../company/company.entity';
import { User } from '../user/user.entity';

@Entity()
@Index(DBIndexes.IDX_RISK_MATRIX_COMPANYID, ['companyId'], {
  where: 'deleted = false',
})
export class RiskMatrixMaster extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'int' })
  public rows: number;

  @Column({ type: 'int' })
  public columns: number;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE })
  public status: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  // Relationship
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  updatedUser: User;
} 