import { EntityRepository, Repository, SelectQueryBuilder, Not } from 'typeorm';
import { InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { TokenPayloadModel, CommonStatus } from 'svm-nest-lib-v3';

import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { StatusCommon } from '../../commons/enums';

@EntityRepository(RiskMatrixMaster)
export class RiskMatrixMasterRepository extends Repository<RiskMatrixMaster> {
  async createRiskMatrix(
    createRiskMatrixDto: CreateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    try {
      // Check if there's already an active risk matrix for this company
      const existingActiveMatrix = await this.findOne({
        where: {
          companyId: token.companyId,
          status: StatusCommon.ACTIVE,
          deleted: false,
        },
      });

      if (existingActiveMatrix && createRiskMatrixDto.status === StatusCommon.ACTIVE) {
        throw new BadRequestException('Only one risk matrix can be active at a time');
      }

      const riskMatrix = this.create({
        ...createRiskMatrixDto,
        companyId: token.companyId,
        createdUserId: token.id,
        status: createRiskMatrixDto.status || StatusCommon.ACTIVE,
      });

      return await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create risk matrix');
    }
  }

  async listRiskMatrix(
    query: ListRiskMatrixQueryDTO,
    token: TokenPayloadModel,
  ): Promise<{ data: RiskMatrixMaster[]; total: number }> {
    try {
      const {
        page = 1,
        pageSize = 20,
        content,
        status,
        sort,
        createdAtFrom,
        createdAtTo,
      } = query;

      const queryBuilder = this.buildListQuery(token.companyId);

      // Search by content (searching in rows/columns)
      if (content) {
        queryBuilder.andWhere('(rm.rows::text ILIKE :content OR rm.columns::text ILIKE :content)', {
          content: `%${content}%`,
        });
      }

      // Filter by status
      if (status) {
        queryBuilder.andWhere('rm.status = :status', { status });
      }

      // Date range filter
      if (createdAtFrom) {
        queryBuilder.andWhere('rm.createdAt >= :createdAtFrom', { createdAtFrom });
      }
      if (createdAtTo) {
        queryBuilder.andWhere('rm.createdAt <= :createdAtTo', { createdAtTo });
      }

      // Sorting
      if (sort) {
        this.applySorting(queryBuilder, sort);
      } else {
        queryBuilder.orderBy('rm.createdAt', 'DESC');
      }

      // Pagination
      if (pageSize !== -1) {
        queryBuilder.skip((page - 1) * pageSize).take(pageSize);
      }

      const [data, total] = await queryBuilder.getManyAndCount();

      return { data, total };
    } catch (error) {
      throw new InternalServerErrorException('Failed to list risk matrices');
    }
  }

  async getDetailRiskMatrixById(id: string): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.createQueryBuilder('rm')
        .leftJoinAndSelect('rm.createdUser', 'createdUser')
        .leftJoinAndSelect('rm.updatedUser', 'updatedUser')
        .where('rm.id = :id', { id })
        .andWhere('rm.deleted = false')
        .getOne();

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      return riskMatrix;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get risk matrix details');
    }
  }

  async updateRiskMatrix(
    id: string,
    updateRiskMatrixDto: UpdateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId: token.companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      // If updating to active status, check if there's already an active matrix
      if (
        updateRiskMatrixDto.status === StatusCommon.ACTIVE &&
        riskMatrix.status !== StatusCommon.ACTIVE
      ) {
        const existingActiveMatrix = await this.findOne({
          where: {
            companyId: token.companyId,
            status: StatusCommon.ACTIVE,
            deleted: false,
            id: Not(id),
          },
        });

        if (existingActiveMatrix) {
          throw new BadRequestException('Only one risk matrix can be active at a time');
        }
      }

      Object.assign(riskMatrix, {
        ...updateRiskMatrixDto,
        updatedUserId: token.id,
        updatedAt: new Date(),
      });

      return await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update risk matrix');
    }
  }

  async deleteRiskMatrix(id: string, companyId: string): Promise<void> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      riskMatrix.deleted = true;

      await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete risk matrix');
    }
  }

  private buildListQuery(companyId: string): SelectQueryBuilder<RiskMatrixMaster> {
    return this.createQueryBuilder('rm')
      .leftJoinAndSelect('rm.createdUser', 'createdUser')
      .leftJoinAndSelect('rm.updatedUser', 'updatedUser')
      .where('rm.companyId = :companyId', { companyId })
      .andWhere('rm.deleted = false');
  }

  private applySorting(queryBuilder: SelectQueryBuilder<RiskMatrixMaster>, sort: string): void {
    const sortFields = sort.split(';');
    sortFields.forEach((field) => {
      const [fieldName, direction] = field.split(':');
      const sortDirection = direction === '1' ? 'ASC' : 'DESC';

      switch (fieldName) {
        case 'rows':
          queryBuilder.addOrderBy('rm.rows', sortDirection);
          break;
        case 'columns':
          queryBuilder.addOrderBy('rm.columns', sortDirection);
          break;
        case 'status':
          queryBuilder.addOrderBy('rm.status', sortDirection);
          break;
        case 'createdAt':
          queryBuilder.addOrderBy('rm.createdAt', sortDirection);
          break;
        default:
          break;
      }
    });
  }
} 