import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';

import { ListQueryDto } from '../../../commons/dtos';
import { StatusCommon } from '../../../commons/enums';

export class ListRiskMatrixQueryDTO extends ListQueryDto {
  @ApiProperty({
    enum: StatusCommon,
    required: false,
    description: 'Filter by status',
  })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: string;
} 