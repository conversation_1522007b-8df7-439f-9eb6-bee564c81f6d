import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, Min, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

import { StatusCommon } from '../../../commons/enums';

export class CreateRiskMatrixDTO {
  @ApiProperty({
    type: Number,
    description: 'Number of rows in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Rows must be a positive integer' })
  rows: number;

  @ApiProperty({
    type: Number,
    description: 'Number of columns in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Columns must be a positive integer' })
  columns: number;

  @ApiProperty({
    enum: StatusCommon,
    description: 'Status of the risk matrix',
    required: false,
    default: StatusCommon.ACTIVE,
  })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: string;
} 