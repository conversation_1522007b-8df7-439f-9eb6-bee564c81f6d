import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RiskMatrixMasterController } from './risk-matrix-master.controller';
import { RiskMatrixMasterService } from './risk-matrix-master.service';
import { RiskMatrixMasterRepository } from './risk-matrix-master.repository';

@Module({
  imports: [TypeOrmModule.forFeature([RiskMatrixMasterRepository])],
  controllers: [RiskMatrixMasterController],
  providers: [RiskMatrixMasterService],
  exports: [],
})
export class RiskMatrixMasterModule {} 